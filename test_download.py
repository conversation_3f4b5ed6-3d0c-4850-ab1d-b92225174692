#!/usr/bin/env python3
"""
Test script for video download functionality
"""
import requests
import time
import json

BASE_URL = "http://localhost:8000"

def test_download_endpoint():
    """Test the download endpoint with a sample URL"""
    
    # Test URL validation first
    print("Testing URL validation...")
    
    # Test invalid URL
    invalid_response = requests.post(f"{BASE_URL}/videos/download", json={
        "url": "https://invalid-site.com/video"
    })
    print(f"Invalid URL response: {invalid_response.status_code}")
    if invalid_response.status_code == 400:
        print("✅ Invalid URL correctly rejected")
    else:
        print("❌ Invalid URL should be rejected")
    
    # Test valid URL format but check if yt-dlp is working
    print("\nTesting valid URL format...")
    
    # Use a short YouTube video for testing
    test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Rick Roll - short video
    
    try:
        response = requests.post(f"{BASE_URL}/videos/download", json={
            "url": test_url,
            "quality": "best[height<=480]",  # Lower quality for faster download
            "format": "mp4"
        })
        
        print(f"Download request response: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Download started successfully")
            print(f"Video ID: {data.get('video_id')}")
            print(f"Status: {data.get('download_status')}")
            
            # Test status endpoint
            video_id = data.get('video_id')
            if video_id:
                print(f"\nChecking download status for video {video_id}...")
                
                for i in range(5):  # Check status 5 times
                    time.sleep(2)
                    status_response = requests.get(f"{BASE_URL}/videos/{video_id}/download-status")
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        print(f"Status check {i+1}: {status_data}")
                    else:
                        print(f"Status check {i+1} failed: {status_response.status_code}")
                        break
            
        elif response.status_code == 400:
            error_data = response.json()
            print(f"⚠️  Download rejected: {error_data.get('detail')}")
            print("This might be expected if the video is not accessible")
            
        else:
            print(f"❌ Unexpected response: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")

def test_api_endpoints():
    """Test that all API endpoints are accessible"""
    
    endpoints = [
        "/health",
        "/videos/",
        "/tags/",
        "/analytics/summary"
    ]
    
    print("Testing API endpoints...")
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            if response.status_code in [200, 404]:  # 404 is OK for empty collections
                print(f"✅ {endpoint}: {response.status_code}")
            else:
                print(f"❌ {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")

if __name__ == "__main__":
    print("🧪 Testing tagTok-v2 Download Functionality")
    print("=" * 50)
    
    # Test basic API endpoints
    test_api_endpoints()
    
    print("\n" + "=" * 50)
    
    # Test download functionality
    test_download_endpoint()
    
    print("\n" + "=" * 50)
    print("✅ Testing completed!")
    print("\nTo test the frontend:")
    print("1. Open http://localhost in your browser")
    print("2. Go to the 'Add Videos' page")
    print("3. Try the 'Download from URL' tab")
    print("4. Test with a YouTube URL")
