2025-06-28 18:05:45 - root - INFO - setup_logging:102 - Logging configured - Level: INFO, File: logs/tagtok_20250628.log
2025-06-28 18:05:46 - main - INFO - lifespan:37 - Starting tagTok application...
2025-06-28 18:05:46 - apscheduler.scheduler - INFO - start:181 - Scheduler started
2025-06-28 18:05:46 - services.scheduler_service - INFO - start:48 - Scheduler service started
2025-06-28 18:05:46 - apscheduler.scheduler - INFO - _real_add_job:895 - Added job "Daily Tag Analysis" to job store "default"
2025-06-28 18:05:46 - apscheduler.scheduler - INFO - _real_add_job:895 - Added job "Weekly Comprehensive Analysis" to job store "default"
2025-06-28 18:05:46 - services.scheduler_service - INFO - _setup_default_jobs:82 - Default scheduled jobs configured
2025-06-28 18:05:46 - main - INFO - lifespan:51 - Scheduler service started successfully
2025-06-28 18:05:46 - main - INFO - lifespan:55 - tagTok application startup complete
2025-06-28 18:06:07 - apscheduler.scheduler - INFO - _real_add_job:895 - Added job "Manual Tag Analysis" to job store "default"
2025-06-28 18:06:07 - services.scheduler_service - INFO - trigger_manual_analysis:213 - Manual tag analysis triggered: manual_analysis_20250628_180607
2025-06-28 18:06:12 - apscheduler.scheduler - INFO - remove_job:641 - Removed job manual_analysis_20250628_180607
2025-06-28 18:06:12 - apscheduler.executors.default - INFO - run_coroutine_job:28 - Running job "Manual Tag Analysis (trigger: date[2025-06-28 18:06:12 UTC], next run at: 2025-06-28 18:06:12 UTC)" (scheduled at 2025-06-28 18:06:12.033383+00:00)
2025-06-28 18:06:12 - services.scheduler_service - INFO - _run_scheduled_tag_analysis:89 - Starting scheduled tag analysis (type: manual)
2025-06-28 18:06:12 - sentence_transformers.SentenceTransformer - INFO - __init__:66 - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-06-28 18:07:21 - sentence_transformers.SentenceTransformer - INFO - __init__:105 - Use pytorch device: cpu
2025-06-28 18:07:21 - services.tag_analysis_service - INFO - run_tag_analysis:40 - Starting tag analysis job 1 (type: manual)
2025-06-28 18:07:21 - services.tag_analysis_service - INFO - _analyze_tag_patterns:108 - Analyzing tag patterns...
2025-06-28 18:07:21 - services.tag_analysis_service - INFO - _analyze_video_content:138 - Analyzing video content...
2025-06-28 18:07:21 - services.tag_analysis_service - INFO - _generate_category_suggestions:166 - Generating category suggestions...
2025-06-28 18:07:42 - services.tag_analysis_service - INFO - _generate_tag_optimizations:204 - Generating tag optimization suggestions...
2025-06-28 18:08:12 - services.tag_analysis_service - ERROR - _get_ollama_merge_suggestions:357 - Error getting merge suggestions from Ollama: HTTPConnectionPool(host='ollama', port=11434): Read timed out. (read timeout=30)
2025-06-28 18:08:12 - services.tag_analysis_service - INFO - _generate_new_tag_suggestions:229 - Generating new tag suggestions...
2025-06-28 18:10:03 - services.tag_analysis_service - INFO - run_tag_analysis:95 - Tag analysis job 1 completed successfully
2025-06-28 18:10:03 - services.scheduler_service - INFO - _run_scheduled_tag_analysis:101 - Scheduled tag analysis completed: job_id=1, status=completed
2025-06-28 18:10:03 - services.scheduler_service - INFO - _auto_apply_suggestions:162 - Auto-applied 1 high-confidence suggestions
2025-06-28 18:10:03 - apscheduler.executors.default - INFO - run_coroutine_job:41 - Job "Manual Tag Analysis (trigger: date[2025-06-28 18:06:12 UTC], next run at: 2025-06-28 18:06:12 UTC)" executed successfully
2025-06-28 20:21:39 - main - INFO - lifespan:60 - Shutting down tagTok application...
2025-06-28 20:21:39 - services.scheduler_service - INFO - stop:58 - Scheduler service stopped
2025-06-28 20:21:39 - main - INFO - lifespan:65 - Scheduler service stopped successfully
2025-06-28 20:21:39 - main - INFO - lifespan:69 - tagTok application shutdown complete
2025-06-28 20:21:39 - apscheduler.scheduler - INFO - shutdown:212 - Scheduler has been shut down
