import logging
import logging.handlers
import os
from datetime import datetime
from typing import Optional

def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
):
    """Setup comprehensive logging configuration for the tag analysis system"""
    
    # Create logs directory if it doesn't exist
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, log_level.upper()))
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file is None:
        log_file = os.path.join(log_dir, f"tagtok_{datetime.now().strftime('%Y%m%d')}.log")
    
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=max_file_size,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)  # File gets all messages
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # Separate handler for tag analysis specific logs
    tag_analysis_logger = logging.getLogger('tag_analysis')
    tag_analysis_file = os.path.join(log_dir, f"tag_analysis_{datetime.now().strftime('%Y%m%d')}.log")
    
    tag_analysis_handler = logging.handlers.RotatingFileHandler(
        tag_analysis_file,
        maxBytes=max_file_size,
        backupCount=backup_count,
        encoding='utf-8'
    )
    tag_analysis_handler.setLevel(logging.DEBUG)
    tag_analysis_handler.setFormatter(formatter)
    tag_analysis_logger.addHandler(tag_analysis_handler)
    tag_analysis_logger.propagate = False  # Don't propagate to root logger
    
    # Scheduler specific logs
    scheduler_logger = logging.getLogger('scheduler')
    scheduler_file = os.path.join(log_dir, f"scheduler_{datetime.now().strftime('%Y%m%d')}.log")
    
    scheduler_handler = logging.handlers.RotatingFileHandler(
        scheduler_file,
        maxBytes=max_file_size,
        backupCount=backup_count,
        encoding='utf-8'
    )
    scheduler_handler.setLevel(logging.DEBUG)
    scheduler_handler.setFormatter(formatter)
    scheduler_logger.addHandler(scheduler_handler)
    scheduler_logger.propagate = False
    
    # Ollama API specific logs
    ollama_logger = logging.getLogger('ollama_api')
    ollama_file = os.path.join(log_dir, f"ollama_api_{datetime.now().strftime('%Y%m%d')}.log")
    
    ollama_handler = logging.handlers.RotatingFileHandler(
        ollama_file,
        maxBytes=max_file_size,
        backupCount=backup_count,
        encoding='utf-8'
    )
    ollama_handler.setLevel(logging.DEBUG)
    ollama_handler.setFormatter(formatter)
    ollama_logger.addHandler(ollama_handler)
    ollama_logger.propagate = False
    
    # Suppress noisy third-party loggers
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('apscheduler').setLevel(logging.INFO)
    
    logging.info(f"Logging configured - Level: {log_level}, File: {log_file}")

class TagAnalysisLogger:
    """Specialized logger for tag analysis operations"""
    
    def __init__(self, name: str = "tag_analysis"):
        self.logger = logging.getLogger(name)
    
    def log_analysis_start(self, job_id: int, job_type: str):
        """Log the start of a tag analysis job"""
        self.logger.info(f"Starting tag analysis job {job_id} (type: {job_type})")
    
    def log_analysis_complete(self, job_id: int, stats: dict):
        """Log the completion of a tag analysis job"""
        self.logger.info(
            f"Tag analysis job {job_id} completed - "
            f"Videos: {stats.get('videos_analyzed', 0)}, "
            f"Tags: {stats.get('tags_analyzed', 0)}, "
            f"Suggestions: {stats.get('suggestions_generated', 0)}"
        )
    
    def log_analysis_error(self, job_id: int, error: Exception):
        """Log an error in tag analysis"""
        self.logger.error(f"Tag analysis job {job_id} failed: {error}", exc_info=True)
    
    def log_ollama_request(self, prompt_type: str, text_length: int):
        """Log Ollama API request"""
        self.logger.debug(f"Ollama request - Type: {prompt_type}, Text length: {text_length}")
    
    def log_ollama_response(self, prompt_type: str, success: bool, response_length: int = 0):
        """Log Ollama API response"""
        status = "success" if success else "failed"
        self.logger.debug(f"Ollama response - Type: {prompt_type}, Status: {status}, Length: {response_length}")
    
    def log_suggestion_created(self, suggestion_type: str, confidence: float, video_id: int = None):
        """Log creation of a tag suggestion"""
        video_info = f" for video {video_id}" if video_id else ""
        self.logger.info(f"Created {suggestion_type} suggestion{video_info} (confidence: {confidence:.2f})")
    
    def log_suggestion_applied(self, suggestion_id: int, suggestion_type: str):
        """Log application of a tag suggestion"""
        self.logger.info(f"Applied {suggestion_type} suggestion {suggestion_id}")
    
    def log_tag_optimization(self, tag_id: int, change_type: str, details: str):
        """Log tag optimization action"""
        self.logger.info(f"Tag optimization - Tag {tag_id}: {change_type} - {details}")

class SchedulerLogger:
    """Specialized logger for scheduler operations"""
    
    def __init__(self, name: str = "scheduler"):
        self.logger = logging.getLogger(name)
    
    def log_scheduler_start(self):
        """Log scheduler startup"""
        self.logger.info("Tag analysis scheduler started")
    
    def log_scheduler_stop(self):
        """Log scheduler shutdown"""
        self.logger.info("Tag analysis scheduler stopped")
    
    def log_job_scheduled(self, job_id: str, job_name: str, next_run: str):
        """Log job scheduling"""
        self.logger.info(f"Scheduled job '{job_name}' ({job_id}) - Next run: {next_run}")
    
    def log_job_triggered(self, job_id: str, trigger_type: str = "scheduled"):
        """Log job trigger"""
        self.logger.info(f"Job {job_id} triggered ({trigger_type})")
    
    def log_job_completed(self, job_id: str, duration: float):
        """Log job completion"""
        self.logger.info(f"Job {job_id} completed in {duration:.2f} seconds")
    
    def log_job_error(self, job_id: str, error: Exception):
        """Log job error"""
        self.logger.error(f"Job {job_id} failed: {error}", exc_info=True)
    
    def log_auto_apply(self, count: int, job_id: str):
        """Log auto-application of suggestions"""
        self.logger.info(f"Auto-applied {count} high-confidence suggestions from job {job_id}")

class OllamaLogger:
    """Specialized logger for Ollama API interactions"""
    
    def __init__(self, name: str = "ollama_api"):
        self.logger = logging.getLogger(name)
    
    def log_request(self, model: str, prompt_length: int, request_type: str):
        """Log Ollama API request"""
        self.logger.debug(f"Ollama request - Model: {model}, Type: {request_type}, Prompt length: {prompt_length}")
    
    def log_response(self, model: str, success: bool, response_time: float, response_length: int = 0):
        """Log Ollama API response"""
        status = "success" if success else "failed"
        self.logger.debug(
            f"Ollama response - Model: {model}, Status: {status}, "
            f"Time: {response_time:.2f}s, Length: {response_length}"
        )
    
    def log_connection_error(self, error: Exception):
        """Log Ollama connection error"""
        self.logger.warning(f"Ollama connection failed: {error}")
    
    def log_parse_error(self, response_text: str, error: Exception):
        """Log JSON parsing error"""
        self.logger.warning(f"Failed to parse Ollama response: {error} - Response: {response_text[:200]}...")
    
    def log_timeout(self, timeout_duration: float):
        """Log request timeout"""
        self.logger.warning(f"Ollama request timed out after {timeout_duration}s")

# Global logger instances
tag_analysis_logger = TagAnalysisLogger()
scheduler_logger = SchedulerLogger()
ollama_logger = OllamaLogger()

def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the specified name"""
    return logging.getLogger(name)
