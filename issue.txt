react refresh:37 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
api.ts:49 API Base URL determined: http://**************/api
injection-topics.js:1 Browsing Topics API not found, nothing to remove on http://**************:3000/, main frame
**************/:1 Access to XMLHttpRequest at 'http://**************/api/health' from origin 'http://**************:3000' has been blocked by CORS policy: The 'Access-Control-Allow-Origin' header contains multiple values '*, *', but only one is allowed.
api.ts:75 Network Error: Cannot reach the server
(anonymous) @ api.ts:75
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
testMobileConnection @ api.ts:86
runConnectionTest @ HomePage.tsx:31
(anonymous) @ HomePage.tsx:35
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
api.ts:90 Connection test failed: Network Error
testMobileConnection @ api.ts:90
await in testMobileConnection
runConnectionTest @ HomePage.tsx:31
(anonymous) @ HomePage.tsx:35
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
api.ts:86 
            
            
           GET http://**************/api/health net::ERR_FAILED 200 (OK)
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
testMobileConnection @ api.ts:86
runConnectionTest @ HomePage.tsx:31
(anonymous) @ HomePage.tsx:35
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
**************/:1 Access to XMLHttpRequest at 'http://**************/api/tags?skip=0&limit=100&sort_by=usage_count&order=desc' from origin 'http://**************:3000' has been blocked by CORS policy: The 'Access-Control-Allow-Origin' header contains multiple values '*, *', but only one is allowed.
api.ts:75 Network Error: Cannot reach the server
(anonymous) @ api.ts:75
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getTags @ api.ts:245
queryFn @ HomePage.tsx:138
fetchFn @ query.ts:457
run @ retryer.ts:153
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
api.ts:245 
            
            
           GET http://**************/api/tags?skip=0&limit=100&sort_by=usage_count&order=desc net::ERR_FAILED 307 (Temporary Redirect)
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getTags @ api.ts:245
queryFn @ HomePage.tsx:138
fetchFn @ query.ts:457
run @ retryer.ts:153
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
**************/:1 Access to XMLHttpRequest at 'http://**************/api/videos?skip=0&limit=24' from origin 'http://**************:3000' has been blocked by CORS policy: The 'Access-Control-Allow-Origin' header contains multiple values '*, *', but only one is allowed.
api.ts:75 Network Error: Cannot reach the server
(anonymous) @ api.ts:75
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getVideos @ api.ts:132
queryFn @ HomePage.tsx:46
fetchFn @ query.ts:457
run @ retryer.ts:153
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
api.ts:132 
            
            
           GET http://**************/api/videos?skip=0&limit=24 net::ERR_FAILED 307 (Temporary Redirect)
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getVideos @ api.ts:132
queryFn @ HomePage.tsx:46
fetchFn @ query.ts:457
run @ retryer.ts:153
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
**************/:1 Access to XMLHttpRequest at 'http://**************/api/health' from origin 'http://**************:3000' has been blocked by CORS policy: The 'Access-Control-Allow-Origin' header contains multiple values '*, *', but only one is allowed.
api.ts:75 Network Error: Cannot reach the server
(anonymous) @ api.ts:75
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
testMobileConnection @ api.ts:86
runConnectionTest @ HomePage.tsx:31
(anonymous) @ HomePage.tsx:35
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
api.ts:90 Connection test failed: Network Error
testMobileConnection @ api.ts:90
await in testMobileConnection
runConnectionTest @ HomePage.tsx:31
(anonymous) @ HomePage.tsx:35
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
api.ts:86 
            
            
           GET http://**************/api/health net::ERR_FAILED 200 (OK)
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
testMobileConnection @ api.ts:86
runConnectionTest @ HomePage.tsx:31
(anonymous) @ HomePage.tsx:35
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
**************/:1 Access to XMLHttpRequest at 'http://**************/api/tags/cloud/data' from origin 'http://**************:3000' has been blocked by CORS policy: The 'Access-Control-Allow-Origin' header contains multiple values '*, *', but only one is allowed.
api.ts:75 Network Error: Cannot reach the server
(anonymous) @ api.ts:75
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getTagCloudData @ api.ts:284
queryFn @ HomePage.tsx:146
fetchFn @ query.ts:457
run @ retryer.ts:153
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
api.ts:284 
            
            
           GET http://**************/api/tags/cloud/data net::ERR_FAILED 200 (OK)
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getTagCloudData @ api.ts:284
queryFn @ HomePage.tsx:146
fetchFn @ query.ts:457
run @ retryer.ts:153
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
**************/:1 Access to XMLHttpRequest at 'http://**************/api/videos?skip=0&limit=24' from origin 'http://**************:3000' has been blocked by CORS policy: The 'Access-Control-Allow-Origin' header contains multiple values '*, *', but only one is allowed.
api.ts:75 Network Error: Cannot reach the server
(anonymous) @ api.ts:75
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getVideos @ api.ts:132
queryFn @ HomePage.tsx:46
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
api.ts:132 
            
            
           GET http://**************/api/videos?skip=0&limit=24 net::ERR_FAILED 307 (Temporary Redirect)
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getVideos @ api.ts:132
queryFn @ HomePage.tsx:46
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
**************/:1 Access to XMLHttpRequest at 'http://**************/api/tags?skip=0&limit=100&sort_by=usage_count&order=desc' from origin 'http://**************:3000' has been blocked by CORS policy: The 'Access-Control-Allow-Origin' header contains multiple values '*, *', but only one is allowed.
api.ts:75 Network Error: Cannot reach the server
(anonymous) @ api.ts:75
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getTags @ api.ts:245
queryFn @ HomePage.tsx:138
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
api.ts:245 
            
            
           GET http://**************/api/tags?skip=0&limit=100&sort_by=usage_count&order=desc net::ERR_FAILED 307 (Temporary Redirect)
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getTags @ api.ts:245
queryFn @ HomePage.tsx:138
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
**************/:1 Access to XMLHttpRequest at 'http://**************/api/tags/cloud/data' from origin 'http://**************:3000' has been blocked by CORS policy: The 'Access-Control-Allow-Origin' header contains multiple values '*, *', but only one is allowed.
api.ts:75 Network Error: Cannot reach the server
(anonymous) @ api.ts:75
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getTagCloudData @ api.ts:284
queryFn @ HomePage.tsx:146
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
api.ts:284 
            
            
           GET http://**************/api/tags/cloud/data net::ERR_FAILED 200 (OK)
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getTagCloudData @ api.ts:284
queryFn @ HomePage.tsx:146
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
**************/:1 Access to XMLHttpRequest at 'http://**************/api/videos?skip=0&limit=24' from origin 'http://**************:3000' has been blocked by CORS policy: The 'Access-Control-Allow-Origin' header contains multiple values '*, *', but only one is allowed.
api.ts:75 Network Error: Cannot reach the server
(anonymous) @ api.ts:75
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getVideos @ api.ts:132
queryFn @ HomePage.tsx:46
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
api.ts:132 
            
            
           GET http://**************/api/videos?skip=0&limit=24 net::ERR_FAILED 307 (Temporary Redirect)
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getVideos @ api.ts:132
queryFn @ HomePage.tsx:46
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
**************/:1 Access to XMLHttpRequest at 'http://**************/api/videos?skip=0&limit=24' from origin 'http://**************:3000' has been blocked by CORS policy: The 'Access-Control-Allow-Origin' header contains multiple values '*, *', but only one is allowed.
api.ts:75 Network Error: Cannot reach the server
(anonymous) @ api.ts:75
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getVideos @ api.ts:132
queryFn @ HomePage.tsx:46
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
api.ts:132 
            
            
           GET http://**************/api/videos?skip=0&limit=24 net::ERR_FAILED 307 (Temporary Redirect)
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getVideos @ api.ts:132
queryFn @ HomePage.tsx:46
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
start @ retryer.ts:218
fetch @ query.ts:576
#executeFetch @ queryObserver.ts:333
onSubscribe @ queryObserver.ts:104
subscribe @ subscribable.ts:11
(anonymous) @ useBaseQuery.ts:99
subscribeToStore @ react-dom.development.js:16139
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
**************/:1 Uncaught (in promise) Error: Could not establish connection. Receiving end does not exist.
**************/:1 Uncaught (in promise) Error: Could not establish connection. Receiving end does not exist.
