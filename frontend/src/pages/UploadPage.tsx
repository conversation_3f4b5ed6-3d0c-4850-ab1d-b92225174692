import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  CloudArrowUpIcon,
  DocumentIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

import { videoApi } from '../utils/api';
import { UploadResponse } from '../types';
import FileDropzone from '../components/FileDropzone';
import LoadingSpinner from '../components/LoadingSpinner';

interface UploadFile {
  file: File;
  id: string;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

const UploadPage: React.FC = () => {
  const navigate = useNavigate();
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState(false);

  const uploadMutation = useMutation({
    mutationFn: (files: FileList) => {
      setIsUploading(true);
      setUploadProgress(0);
      return videoApi.uploadVideos(files, (progress) => {
        setUploadProgress(progress);
      });
    },
    onSuccess: (response: UploadResponse) => {
      setIsUploading(false);
      setUploadProgress(100);
      toast.success(`Successfully uploaded ${response.total_uploaded} videos`);

      // Update file statuses
      setUploadFiles(prev => prev.map(uploadFile => {
        if (response.uploaded_files.includes(uploadFile.file.name)) {
          return { ...uploadFile, status: 'success' };
        }
        const failedFile = response.failed_files.find(f => f.filename === uploadFile.file.name);
        if (failedFile) {
          return { ...uploadFile, status: 'error', error: failedFile.error };
        }
        return uploadFile;
      }));

      // Show failed files if any
      if (response.failed_files.length > 0) {
        response.failed_files.forEach(failedFile => {
          toast.error(`Failed to upload ${failedFile.filename}: ${failedFile.error}`);
        });
      }

      // Navigate to home after successful upload
      if (response.total_uploaded > 0) {
        setTimeout(() => {
          navigate('/');
        }, 2000);
      }
    },
    onError: (error: any) => {
      setIsUploading(false);
      setUploadProgress(0);
      toast.error('Upload failed: ' + (error.response?.data?.detail || error.message));
      setUploadFiles(prev => prev.map(file => ({ ...file, status: 'error', error: 'Upload failed' })));
    },
  });

  const handleFilesSelected = (files: FileList) => {
    const newUploadFiles: UploadFile[] = Array.from(files).map(file => ({
      file,
      id: `${file.name}-${Date.now()}`,
      status: 'pending',
    }));

    setUploadFiles(prev => [...prev, ...newUploadFiles]);
  };

  const handleRemoveFile = (id: string) => {
    setUploadFiles(prev => prev.filter(file => file.id !== id));
  };

  const handleUpload = () => {
    if (uploadFiles.length === 0) return;

    // Create FileList from upload files
    const dt = new DataTransfer();
    uploadFiles.forEach(uploadFile => {
      dt.items.add(uploadFile.file);
    });

    // Update status to uploading
    setUploadFiles(prev => prev.map(file => ({ ...file, status: 'uploading' })));

    uploadMutation.mutate(dt.files);
  };

  const handleClearAll = () => {
    setUploadFiles([]);
    setUploadProgress(0);
    setIsUploading(false);
  };

  const formatFileSize = (bytes: number): string => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const getStatusIcon = (status: UploadFile['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'uploading':
        return <LoadingSpinner size="sm" />;
      default:
        return <DocumentIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const canUpload = uploadFiles.length > 0 && !uploadMutation.isPending;
  const totalSize = uploadFiles.reduce((sum, file) => sum + file.file.size, 0);

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Upload Videos</h1>
        <p className="mt-2 text-sm text-gray-700">
          Upload your video files to automatically transcribe and tag them with AI
        </p>
      </div>

      {/* Upload Area */}
      <div className="bg-white rounded-lg shadow p-6">
        <FileDropzone
          onFilesSelected={handleFilesSelected}
          accept="video/*"
          multiple
          disabled={uploadMutation.isPending}
        />
      </div>

          {/* Upload Summary */}
          {uploadFiles.length > 0 && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {uploadFiles.length} file{uploadFiles.length !== 1 ? 's' : ''} selected
                </p>
                <p className="text-sm text-gray-500">
                  Total size: {formatFileSize(totalSize)}
                </p>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={handleClearAll}
                  disabled={uploadMutation.isPending}
                  className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                >
                  Clear All
                </button>
                <button
                  onClick={handleUpload}
                  disabled={!canUpload}
                  className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {uploadMutation.isPending ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                      Upload Videos
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Upload Progress Bar */}
            {isUploading && (
              <div className="mt-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Upload Progress</span>
                  <span className="text-sm font-medium text-gray-700">{uploadProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary-600 h-2 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${uploadProgress}%` }}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {uploadProgress < 100 ? 'Uploading files...' : 'Processing videos...'}
                </p>
              </div>
            )}
          </div>
        )}

        {/* File List */}
        {uploadFiles.length > 0 && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Files to Upload</h3>
            </div>
            <div className="divide-y divide-gray-200">
              {uploadFiles.map((uploadFile) => (
                <div key={uploadFile.id} className="px-6 py-4 flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(uploadFile.status)}
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {uploadFile.file.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {formatFileSize(uploadFile.file.size)}
                      </p>
                      {uploadFile.error && (
                        <p className="text-sm text-red-600 mt-1">
                          {uploadFile.error}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500 capitalize">
                        {uploadFile.status}
                      </span>
                      {uploadFile.status === 'pending' && (
                        <button
                          onClick={() => handleRemoveFile(uploadFile.id)}
                          disabled={uploadMutation.isPending}
                          className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
                        >
                          <XMarkIcon className="h-5 w-5" />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

      {/* Upload Instructions */}
      <div className="bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-blue-900 mb-2">What happens after upload?</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Videos are automatically transcribed using AI (Whisper)</li>
          <li>• Smart tags are generated based on the transcript content</li>
          <li>• Thumbnails are extracted for easy browsing</li>
          <li>• You can edit tags and add custom ones later</li>
          <li>• Processing may take a few minutes depending on video length</li>
        </ul>
      </div>
    </div>
  );
};

export default UploadPage;
